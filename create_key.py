#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成授权文件 key.vdf
"""

import os
import sys
from datetime import datetime
import tkinter as tk
from tkinter import messagebox
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding

def create_key():
    """创建授权文件"""
    # 密钥生成配置（必须与验证时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 当前时间作为授权开始时间
    current_time = datetime.now().isoformat()
    print(f"授权开始时间: {current_time}")
    
    # 准备数据并填充
    data = current_time.encode('utf-8')
    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(data) + padder.finalize()
    
    # 加密数据
    iv = os.urandom(16)  # 随机初始化向量
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    encryptor = cipher.encryptor()
    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    
    # 将IV和密文写入文件
    with open("key.vdf", "wb") as f:
        f.write(iv + ciphertext)
    
    print(f"授权文件 key.vdf 已创建成功！有效期30天")
    
    # 显示成功消息
    root = tk.Tk()
    root.withdraw()
    messagebox.showinfo("成功", "授权文件 key.vdf 已创建成功！有效期30天")
    root.destroy()

if __name__ == "__main__":
    create_key() 
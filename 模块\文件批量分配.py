#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🗂️ 文件批量分配模块 🗂️
功能: 将指定文件夹中的文件按照指定数量分配到新创建的子文件夹中
"""

import os
import shutil
import logging
import random
import time
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QLineEdit, QFileDialog, QSpinBox, 
                            QProgressBar, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex, QWaitCondition

class FileDistributionThread(QThread):
    """文件分配处理线程"""
    progress_signal = pyqtSignal(int)
    status_signal = pyqtSignal(str)
    complete_signal = pyqtSignal(bool, str)
    
    def __init__(self, source_folder, name, files_per_group):
        super().__init__()
        self.source_folder = source_folder
        self.name = name
        self.files_per_group = files_per_group
        self.is_running = True
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
        self.condition.wakeAll()
        
    def run(self):
        try:
            # 获取源文件夹中的所有文件
            self.status_signal.emit("正在扫描文件...")
            all_files = []
            
            # 检查是否被中断
            if not self.is_running:
                return
                
            for filename in os.listdir(self.source_folder):
                file_path = os.path.join(self.source_folder, filename)
                if os.path.isfile(file_path):
                    all_files.append(filename)
            
            # 如果没有文件，则返回错误
            if not all_files:
                self.complete_signal.emit(False, "所选文件夹中没有文件")
                return
                
            # 随机打乱文件顺序
            random.shuffle(all_files)
            
            # 计算需要创建的文件夹数量
            folder_count = (len(all_files) + self.files_per_group - 1) // self.files_per_group
            
            # 获取当前日期
            current_date = datetime.now().strftime("%m.%d")
            
            self.status_signal.emit(f"开始分配 {len(all_files)} 个文件到 {folder_count} 个文件夹...")
            
            # 创建子文件夹并分配文件
            processed_files = 0
            last_update_time = time.time()
            
            for i in range(folder_count):
                # 检查是否被中断
                if not self.is_running:
                    self.status_signal.emit("文件分配已取消")
                    return
                    
                # 创建子文件夹
                subfolder_name = f"{current_date}{self.name}_{i+1}"
                subfolder_path = os.path.join(self.source_folder, subfolder_name)
                
                # 如果文件夹已存在，则添加随机数以避免冲突
                if os.path.exists(subfolder_path):
                    subfolder_name = f"{current_date}{self.name}_{i+1}_{random.randint(100, 999)}"
                    subfolder_path = os.path.join(self.source_folder, subfolder_name)
                
                os.makedirs(subfolder_path)
                self.status_signal.emit(f"创建子文件夹: {subfolder_name}")
                
                # 计算当前子文件夹应分配的文件数量
                start_index = i * self.files_per_group
                end_index = min((i + 1) * self.files_per_group, len(all_files))
                current_group_files = all_files[start_index:end_index]
                
                # 移动文件到子文件夹
                for filename in current_group_files:
                    # 检查是否被中断
                    if not self.is_running:
                        self.status_signal.emit("文件分配已取消")
                        return
                        
                    source_path = os.path.join(self.source_folder, filename)
                    target_path = os.path.join(subfolder_path, filename)
                    shutil.move(source_path, target_path)
                    processed_files += 1
                    
                    # 限制进度更新频率，避免UI卡顿
                    current_time = time.time()
                    if current_time - last_update_time > 0.1 or processed_files == len(all_files):
                        # 更新进度
                        progress_percent = int(processed_files * 100 / len(all_files))
                        self.progress_signal.emit(progress_percent)
                        last_update_time = current_time
                        # 处理事件，确保UI响应
                        QApplication.processEvents()
            
            complete_message = f"文件分配完成！共处理 {len(all_files)} 个文件，创建了 {folder_count} 个子文件夹。"
            self.status_signal.emit(complete_message)
            self.complete_signal.emit(True, complete_message)
            
        except Exception as e:
            error_message = f"文件分配过程中出错: {str(e)}"
            self.status_signal.emit(error_message)
            self.complete_signal.emit(False, error_message)

class FileDistribution(QWidget):
    """文件批量分配功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.worker_thread = None
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能可以将指定文件夹中的文件按照指定数量分配到新创建的子文件夹中")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(10)
        
        # 源文件夹选择
        source_folder_layout = QHBoxLayout()
        source_folder_label = QLabel("选择源文件夹:")
        self.source_folder_input = QLineEdit()
        self.source_folder_input.setReadOnly(True)
        source_folder_button = QPushButton("浏览...")
        source_folder_button.clicked.connect(self.select_source_folder)
        
        source_folder_layout.addWidget(source_folder_label)
        source_folder_layout.addWidget(self.source_folder_input)
        source_folder_layout.addWidget(source_folder_button)
        form_layout.addLayout(source_folder_layout)
        
        # 名称输入
        name_layout = QHBoxLayout()
        name_label = QLabel("输入名称:")
        self.name_input = QLineEdit()
        self.name_input.textChanged.connect(self.save_config)
        # 默认值将在load_config中设置
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input)
        form_layout.addLayout(name_layout)
        
        # 每组文件数输入
        files_count_layout = QHBoxLayout()
        files_count_label = QLabel("每组文件数:")
        self.files_count_input = QSpinBox()
        self.files_count_input.setRange(1, 10000)
        self.files_count_input.valueChanged.connect(self.save_config)
        # 默认值将在load_config中设置
        files_count_layout.addWidget(files_count_label)
        files_count_layout.addWidget(self.files_count_input)
        form_layout.addLayout(files_count_layout)
        
        # 添加快捷分组按钮
        quick_split_layout = QHBoxLayout()
        quick_split_label = QLabel("快速平均分组:")
        quick_split_layout.addWidget(quick_split_label)
        
        # 添加快捷分组按钮 (2-5份)
        for splits in range(2, 6):
            split_button = QPushButton(f"{splits}份")
            split_button.clicked.connect(lambda checked, s=splits: self.quick_split(s))
            quick_split_layout.addWidget(split_button)
        
        form_layout.addLayout(quick_split_layout)
        
        main_layout.addLayout(form_layout)
        
        # 添加状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        
        # 添加开始按钮
        self.start_button = QPushButton("开始分配")
        self.start_button.clicked.connect(self.start_distribution)
        button_layout.addWidget(self.start_button)
        
        # 添加取消按钮
        self.cancel_button = QPushButton("取消分配")
        self.cancel_button.clicked.connect(self.cancel_distribution)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
    
    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('file_distribution', {})
            
            # 设置默认名称
            default_name = config.get('default_name', '吴胜雄')
            self.name_input.setText(default_name)
            
            # 设置默认文件数
            default_count = config.get('default_count', 1000)
            self.files_count_input.setValue(default_count)
            
            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.source_folder_input.setText(last_folder)
        else:
            # 如果没有配置，设置默认值
            self.name_input.setText('吴胜雄')
            self.files_count_input.setValue(1000)
    
    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'file_distribution' not in self.parent.config:
                self.parent.config['file_distribution'] = {}
            
            # 保存当前设置
            self.parent.config['file_distribution']['default_name'] = self.name_input.text()
            self.parent.config['file_distribution']['default_count'] = self.files_count_input.value()
            self.parent.config['file_distribution']['last_folder'] = self.source_folder_input.text()
    
    def quick_split(self, num_splits):
        """根据文件总数快速平均分配为指定份数"""
        source_folder = self.source_folder_input.text()
        
        # 验证源文件夹
        if not source_folder or not os.path.isdir(source_folder):
            QMessageBox.warning(self, "警告", "请先选择有效的源文件夹")
            return
            
        try:
            # 统计源文件夹中的文件数量
            file_count = 0
            for filename in os.listdir(source_folder):
                if os.path.isfile(os.path.join(source_folder, filename)):
                    file_count += 1
                    
            # 如果没有文件，提示用户
            if file_count == 0:
                QMessageBox.warning(self, "警告", "所选文件夹中没有文件")
                return
                
            # 计算每份的文件数量（向上取整以确保覆盖所有文件）
            files_per_group = (file_count + num_splits - 1) // num_splits
            
            # 设置到输入框
            self.files_count_input.setValue(files_per_group)
            
            # 显示成功消息
            QMessageBox.information(self, "平均分组设置成功", 
                                   f"已将{file_count}个文件平均分为{num_splits}份，每份约{files_per_group}个文件。")
            logging.info(f"已设置平均分组：{num_splits}份，共{file_count}个文件，每份约{files_per_group}个文件")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"计算平均分组时出错: {str(e)}")
            logging.error(f"计算平均分组时出错: {str(e)}")
    
    def select_source_folder(self):
        """选择源文件夹"""
        # 从上次的文件夹开始浏览
        start_dir = self.source_folder_input.text() if self.source_folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择源文件夹", start_dir)
        if folder_path:
            self.source_folder_input.setText(folder_path)
            logging.info(f"已选择源文件夹: {folder_path}")
            # 保存当前文件夹路径
            self.save_config()
    
    def start_distribution(self):
        """开始文件分配处理"""
        # 获取输入参数
        source_folder = self.source_folder_input.text()
        name = self.name_input.text()
        files_per_group = self.files_count_input.value()
        
        # 验证输入
        if not source_folder:
            QMessageBox.warning(self, "警告", "请选择源文件夹")
            logging.warning("未选择源文件夹")
            return
            
        if not name:
            QMessageBox.warning(self, "警告", "请输入名称")
            logging.warning("未输入名称")
            return
        
        # 保存当前配置
        self.save_config()
        
        # 重置状态和进度条
        self.status_label.setText("准备分配文件...")
        self.progress_bar.setValue(0)
        
        # 禁用开始按钮，启用取消按钮
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        
        # 创建并启动工作线程
        self.worker_thread = FileDistributionThread(source_folder, name, files_per_group)
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.status_signal.connect(self.update_status)
        self.worker_thread.complete_signal.connect(self.handle_complete)
        self.worker_thread.start()
        
        logging.info(f"开始分配文件: 源文件夹={source_folder}, 名称={name}, 每组文件数={files_per_group}")
    
    def cancel_distribution(self):
        """取消文件分配处理"""
        if self.worker_thread and self.worker_thread.isRunning():
            # 停止工作线程
            self.worker_thread.stop()
            # 等待线程结束
            self.worker_thread.wait()
            # 更新状态
            self.status_label.setText("文件分配已取消")
            # 启用开始按钮，禁用取消按钮
            self.start_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            logging.info("文件分配已取消")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)
    
    def handle_complete(self, success, message):
        """处理完成回调"""
        # 重置进度条
        self.progress_bar.setValue(100 if success else 0)
        
        # 更新状态
        self.status_label.setText(message)
        
        # 启用开始按钮，禁用取消按钮
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.critical(self, "错误", message)
    
    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.worker_thread.wait()
        event.accept()

# 为了兼容性，保留原来的类名
文件批量分配 = FileDistribution
文件分配线程 = FileDistributionThread 
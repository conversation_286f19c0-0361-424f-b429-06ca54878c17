# Python工具百宝箱

一个多功能的Python工具集合，具有现代化GUI界面，包含多种实用功能。

## 功能特点

- **模块化设计**：每个功能独立成模块，方便扩展
- **现代化界面**：使用PyQt5构建的美观界面
- **中文界面**：全中文界面，操作简单直观
- **详细日志**：带有时间戳和颜色区分的日志系统
- **配置保存**：自动保存各模块配置，下次使用时自动加载

## 当前功能

1. **程序快捷启动**：添加常用程序快捷方式，一键启动
2. **文件批量分配**：将文件夹中的文件按指定数量分配到子文件夹中
3. **图片加白底**：为透明背景的图片添加纯白色背景
4. **中文文件删除**：快速查找并删除包含中文字符的文件
5. **图片分类**：自动将图片按照横竖方向分类到不同文件夹

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行方法

```bash
python main.py
```

## 使用说明

### 程序快捷启动

1. 点击"添加程序"按钮选择需要添加的程序
2. 程序会显示在界面上的按钮中
3. 点击对应按钮即可快速启动程序
4. 程序路径会自动保存在配置文件中

### 文件批量分配

1. 选择需要处理的源文件夹
2. 输入名称（如"小明"）
3. 设置每组文件数量
4. 点击"开始分配"按钮
5. 程序会在源文件夹中创建子文件夹（如"05.30小明_1"），并将文件移动到子文件夹中
6. 配置会自动保存，下次使用时自动加载上次的名称、数量和文件夹

### 图片加白底

1. 选择包含图片的文件夹
2. 选择是否保留原图（如不保留，将直接替换原图）
3. 点击"开始处理"按钮
4. 程序会为所有透明背景的图片添加白色背景
5. 配置会自动保存，下次使用时自动加载上次的文件夹和选项

### 中文文件删除

1. 选择需要处理的文件夹
2. 选择是否自动删除（如不选择，将只显示文件列表而不删除）
3. 点击"开始扫描"按钮
4. 程序会找出所有包含中文字符的文件，并根据设置删除或显示
5. 配置会自动保存，下次使用时自动加载上次的文件夹和选项

### 图片分类

1. 选择包含图片的文件夹
2. 设置宽高差阈值（当图片宽度减去高度大于此值时，判定为横图）
3. 点击"开始分类"按钮
4. 程序会自动将图片分类到"横图"和"竖图"两个子文件夹中
5. 配置会自动保存，下次使用时自动加载上次的文件夹和阈值设置

## 配置文件

程序会自动创建和维护`config.json`配置文件，保存各模块的设置：

```json
{
    "program_launcher": {
        "programs": [
            {"path": "程序路径", "name": "程序名称"},
            ...
        ]
    },
    "file_distribution": {
        "default_name": "默认名称",
        "default_count": 1000,
        "last_folder": "上次使用的文件夹"
    },
    "add_white_bg": {
        "last_folder": "上次使用的文件夹",
        "keep_original": false
    },
    "chinese_file_remover": {
        "last_folder": "上次使用的文件夹",
        "auto_delete": false
    },
    "image_classifier": {
        "last_folder": "上次使用的文件夹",
        "threshold": 200
    }
}
```

## 打包成可执行文件

可以使用PyInstaller将程序打包成单个可执行文件：

```bash
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

## 开发说明

本项目采用模块化设计，如需添加新功能，只需按照以下步骤操作：

1. 在"模块"文件夹中创建新的功能模块文件
2. 在模块中实现配置保存和加载功能
3. 在`__init__.py`中导出模块
4. 在`main.py`中导入并添加到选项卡中
5. 在`MainWindow.load_config()`方法中添加默认配置 